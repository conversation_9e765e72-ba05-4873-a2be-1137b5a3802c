#!/usr/bin/env python3
"""
Telegram Notifier for Enhanced Live Trading System
Sends real-time notifications for trades, alerts, and system status.
"""

import asyncio
import logging
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional
import httpx

logger = logging.getLogger(__name__)

class TelegramNotifier:
    """Enhanced Telegram notifier for live trading system."""
    
    def __init__(self, bot_token: Optional[str] = None, chat_id: Optional[str] = None):
        """Initialize Telegram notifier."""
        self.bot_token = bot_token or os.getenv('TELEGRAM_BOT_TOKEN')
        self.chat_id = chat_id or os.getenv('TELEGRAM_CHAT_ID')
        
        # Check if credentials are available
        self.enabled = bool(self.bot_token and self.chat_id)
        
        if not self.enabled:
            logger.warning("Telegram credentials not found - notifications disabled")
        else:
            logger.info(f"Telegram notifier initialized for chat {self.chat_id}")
        
        # HTTP client for API calls
        self.http_client = httpx.AsyncClient(timeout=30.0)
        
        # Rate limiting
        self.last_notification_time = {}
        self.rate_limit_seconds = 60  # Minimum time between similar notifications
    
    async def close(self):
        """Close the HTTP client."""
        await self.http_client.aclose()
    
    def _should_send_notification(self, notification_type: str) -> bool:
        """Check if notification should be sent based on rate limiting."""
        if not self.enabled:
            return False
        
        now = datetime.now()
        last_time = self.last_notification_time.get(notification_type)
        
        if last_time is None:
            self.last_notification_time[notification_type] = now
            return True
        
        time_diff = (now - last_time).total_seconds()
        if time_diff >= self.rate_limit_seconds:
            self.last_notification_time[notification_type] = now
            return True
        
        return False
    
    async def send_message(self, message: str, parse_mode: str = "Markdown") -> bool:
        """Send a message to Telegram."""
        if not self.enabled:
            logger.debug(f"Telegram disabled, would send: {message}")
            return False
        
        try:
            url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
            data = {
                "chat_id": self.chat_id,
                "text": message,
                "parse_mode": parse_mode
            }
            
            response = await self.http_client.post(url, json=data)
            response.raise_for_status()
            
            result = response.json()
            if result.get("ok"):
                logger.debug("Telegram message sent successfully")
                return True
            else:
                logger.error(f"Telegram API error: {result.get('description')}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending Telegram message: {e}")
            return False
    
    async def notify_trade_executed(self, trade_data: Dict[str, Any]) -> bool:
        """Send notification for executed trade."""
        if not self._should_send_notification("trade_executed"):
            return False
        
        try:
            signal = trade_data.get('signal', {})
            position_data = trade_data.get('position_data', {})
            tx_result = trade_data.get('transaction_result', {})
            
            action = signal.get('action', 'UNKNOWN')
            size_sol = position_data.get('position_size_sol', 0)
            size_usd = position_data.get('position_size_usd', 0)
            confidence = signal.get('confidence', 0)
            signature = tx_result.get('signature', 'N/A')
            execution_time = tx_result.get('execution_time', 0)
            
            # Format message
            emoji = "🟢" if action == "BUY" else "🔴"
            message = f"""
{emoji} *TRADE EXECUTED* {emoji}

*Action*: {action}
*Size*: {size_sol:.4f} SOL (${size_usd:.2f})
*Confidence*: {confidence:.1%}
*Execution Time*: {execution_time:.3f}s

*Signature*: `{signature[:16]}...{signature[-16:] if len(signature) > 32 else signature}`

*Time*: {datetime.now().strftime('%H:%M:%S')}
"""
            
            return await self.send_message(message)
            
        except Exception as e:
            logger.error(f"Error sending trade notification: {e}")
            return False
    
    async def notify_trade_rejected(self, signal: Dict[str, Any], reason: str) -> bool:
        """Send notification for rejected trade."""
        if not self._should_send_notification("trade_rejected"):
            return False
        
        try:
            action = signal.get('action', 'UNKNOWN')
            size = signal.get('size', 0)
            
            message = f"""
⚠️ *TRADE REJECTED* ⚠️

*Action*: {action}
*Size*: {size:.4f} SOL
*Reason*: {reason}

*Time*: {datetime.now().strftime('%H:%M:%S')}
"""
            
            return await self.send_message(message)
            
        except Exception as e:
            logger.error(f"Error sending rejection notification: {e}")
            return False
    
    async def notify_session_started(self, duration_hours: Optional[float] = None) -> bool:
        """Send notification when trading session starts."""
        try:
            duration_text = f"for {duration_hours} hours" if duration_hours else "indefinitely"
            
            message = f"""
🚀 *ENHANCED LIVE TRADING STARTED* 🚀

*Session Duration*: {duration_text}
*Start Time*: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
*Mode*: Production Trading

Ready to execute real transactions! 💰
"""
            
            return await self.send_message(message)
            
        except Exception as e:
            logger.error(f"Error sending session start notification: {e}")
            return False
    
    async def notify_session_ended(self, metrics: Dict[str, Any]) -> bool:
        """Send notification when trading session ends."""
        try:
            cycles_completed = metrics.get('cycles_completed', 0)
            trades_executed = metrics.get('trades_executed', 0)
            trades_rejected = metrics.get('trades_rejected', 0)
            session_duration = metrics.get('session_duration_minutes', 0)
            
            success_rate = (trades_executed / (trades_executed + trades_rejected) * 100) if (trades_executed + trades_rejected) > 0 else 0
            
            message = f"""
🏁 *TRADING SESSION COMPLETED* 🏁

*Duration*: {session_duration:.1f} minutes
*Cycles*: {cycles_completed}
*Trades Executed*: {trades_executed}
*Trades Rejected*: {trades_rejected}
*Success Rate*: {success_rate:.1f}%

*End Time*: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            return await self.send_message(message)
            
        except Exception as e:
            logger.error(f"Error sending session end notification: {e}")
            return False
    
    async def notify_error(self, error_message: str, component: str = "System") -> bool:
        """Send notification for system errors."""
        if not self._should_send_notification("error"):
            return False
        
        try:
            message = f"""
🚨 *SYSTEM ERROR* 🚨

*Component*: {component}
*Error*: {error_message}

*Time*: {datetime.now().strftime('%H:%M:%S')}
"""
            
            return await self.send_message(message)
            
        except Exception as e:
            logger.error(f"Error sending error notification: {e}")
            return False
    
    async def notify_daily_summary(self, summary_data: Dict[str, Any]) -> bool:
        """Send daily trading summary."""
        try:
            total_trades = summary_data.get('total_trades', 0)
            successful_trades = summary_data.get('successful_trades', 0)
            total_volume = summary_data.get('total_volume_usd', 0)
            pnl = summary_data.get('total_pnl_usd', 0)
            
            success_rate = (successful_trades / total_trades * 100) if total_trades > 0 else 0
            pnl_emoji = "📈" if pnl >= 0 else "📉"
            
            message = f"""
📊 *DAILY TRADING SUMMARY* 📊

*Total Trades*: {total_trades}
*Successful*: {successful_trades} ({success_rate:.1f}%)
*Volume*: ${total_volume:.2f}
*P&L*: {pnl_emoji} ${pnl:.2f}

*Date*: {datetime.now().strftime('%Y-%m-%d')}
"""
            
            return await self.send_message(message)
            
        except Exception as e:
            logger.error(f"Error sending daily summary: {e}")
            return False
    
    async def test_connection(self) -> bool:
        """Test Telegram connection."""
        if not self.enabled:
            logger.warning("Telegram not configured")
            return False
        
        test_message = f"""
🧪 *TEST MESSAGE* 🧪

Synergy7 Enhanced Trading System
Connection test successful!

*Time*: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        return await self.send_message(test_message)


# Global instance for easy access
_telegram_notifier = None

def get_telegram_notifier() -> TelegramNotifier:
    """Get the global Telegram notifier instance."""
    global _telegram_notifier
    
    if _telegram_notifier is None:
        _telegram_notifier = TelegramNotifier()
    
    return _telegram_notifier
