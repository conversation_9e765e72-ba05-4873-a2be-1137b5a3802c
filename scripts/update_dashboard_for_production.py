#!/usr/bin/env python3
"""
Dashboard Update Script for Production 0.5 Wallet Strategy
Updates dashboard metrics and displays to align with the new production strategy.
"""

import streamlit as st
import json
import os
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProductionDashboard:
    """Enhanced dashboard for production 0.5 wallet strategy monitoring."""
    
    def __init__(self):
        """Initialize the production dashboard."""
        self.data_sources = {
            'live_production': 'output/live_production/dashboard/',
            'paper_trading': 'output/paper_trading/dashboard/',
            'wallet': 'output/wallet/',
            'trades': 'output/live_trade_test/trades/'
        }
        
        # Dashboard configuration
        st.set_page_config(
            page_title="Synergy7 Production Trading Dashboard",
            page_icon="🚀",
            layout="wide",
            initial_sidebar_state="expanded"
        )
    
    def load_production_metrics(self) -> Dict[str, Any]:
        """Load production trading metrics."""
        try:
            metrics_file = os.path.join(self.data_sources['live_production'], 'performance_metrics.json')
            if os.path.exists(metrics_file):
                with open(metrics_file, 'r') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"Error loading production metrics: {e}")
            return {}
    
    def load_wallet_data(self) -> Dict[str, Any]:
        """Load current wallet data."""
        try:
            wallet_file = os.path.join(self.data_sources['wallet'], 'wallet_balance.json')
            if os.path.exists(wallet_file):
                with open(wallet_file, 'r') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"Error loading wallet data: {e}")
            return {}
    
    def load_latest_cycle(self) -> Dict[str, Any]:
        """Load latest trading cycle data."""
        try:
            cycle_file = os.path.join(self.data_sources['live_production'], 'latest_cycle.json')
            if os.path.exists(cycle_file):
                with open(cycle_file, 'r') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"Error loading latest cycle: {e}")
            return {}
    
    def render_header(self):
        """Render dashboard header."""
        st.title("🚀 Synergy7 Production Trading Dashboard")
        st.markdown("### Live Trading with 0.5 Wallet Strategy")
        
        # Status indicators
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("System Status", "🟢 LIVE", delta="Production")
        
        with col2:
            st.metric("Strategy", "0.5 Wallet", delta="Optimized")
        
        with col3:
            current_time = datetime.now().strftime("%H:%M:%S")
            st.metric("Current Time", current_time, delta="Real-time")
        
        with col4:
            st.metric("Mode", "Production", delta="Real Assets")
    
    def render_wallet_overview(self, wallet_data: Dict[str, Any], production_metrics: Dict[str, Any]):
        """Render wallet overview section."""
        st.header("💰 Wallet Overview")
        
        # Get wallet balance
        wallet_balance = wallet_data.get('wallet_balance', {})
        trading_wallet = wallet_balance.get('trading_wallet', 0.0)
        
        # Calculate 0.5 strategy allocation
        active_capital = trading_wallet * 0.5
        reserve_balance = trading_wallet * 0.5
        
        # Get current SOL price (default to $180)
        sol_price = 180.0  # Would be updated from market data
        
        # Portfolio status from production metrics
        portfolio_status = production_metrics.get('portfolio_status', {})
        current_exposure = portfolio_status.get('current_exposure_sol', 0.0)
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                "Total Wallet",
                f"{trading_wallet:.4f} SOL",
                delta=f"${trading_wallet * sol_price:.2f} USD"
            )
        
        with col2:
            st.metric(
                "Active Capital",
                f"{active_capital:.4f} SOL",
                delta=f"${active_capital * sol_price:.2f} USD"
            )
        
        with col3:
            st.metric(
                "Reserve Balance",
                f"{reserve_balance:.4f} SOL",
                delta=f"${reserve_balance * sol_price:.2f} USD"
            )
        
        with col4:
            exposure_pct = (current_exposure / trading_wallet * 100) if trading_wallet > 0 else 0
            st.metric(
                "Current Exposure",
                f"{current_exposure:.4f} SOL",
                delta=f"{exposure_pct:.1f}% of wallet"
            )
        
        # Wallet allocation chart
        if trading_wallet > 0:
            fig = go.Figure(data=[go.Pie(
                labels=['Active Capital', 'Reserve Balance', 'Current Exposure'],
                values=[active_capital - current_exposure, reserve_balance, current_exposure],
                hole=0.3,
                marker_colors=['#00cc96', '#636efa', '#ff6692']
            )])
            
            fig.update_layout(
                title="Wallet Allocation (0.5 Strategy)",
                height=300
            )
            
            st.plotly_chart(fig, use_container_width=True)
    
    def render_performance_metrics(self, production_metrics: Dict[str, Any]):
        """Render performance metrics section."""
        st.header("📈 Performance Metrics")
        
        # Get performance data
        performance = production_metrics.get('performance_metrics', {})
        session_stats = production_metrics.get('session_stats', {})
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            total_trades = performance.get('total_trades', 0)
            st.metric("Total Trades", total_trades)
        
        with col2:
            win_rate = performance.get('win_rate', 0.0) * 100
            st.metric("Win Rate", f"{win_rate:.1f}%")
        
        with col3:
            daily_pnl = session_stats.get('daily_pnl_usd', 0.0)
            st.metric("Daily P&L", f"${daily_pnl:.2f}")
        
        with col4:
            active_capital_usd = session_stats.get('active_capital_usd', 0.0)
            daily_return = (daily_pnl / active_capital_usd * 100) if active_capital_usd > 0 else 0
            st.metric("Daily Return", f"{daily_return:.2f}%", delta="On Active Capital")
        
        # Performance chart
        if total_trades > 0:
            # Create sample performance data (would be real data in production)
            dates = pd.date_range(start=datetime.now() - timedelta(days=7), end=datetime.now(), freq='H')
            cumulative_pnl = [0] + [daily_pnl * (i / len(dates)) for i in range(1, len(dates))]
            
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=dates,
                y=cumulative_pnl,
                mode='lines',
                name='Cumulative P&L',
                line=dict(color='#00cc96', width=2)
            ))
            
            fig.update_layout(
                title="Cumulative P&L (Active Capital)",
                xaxis_title="Time",
                yaxis_title="P&L (USD)",
                height=300
            )
            
            st.plotly_chart(fig, use_container_width=True)
    
    def render_trading_activity(self, latest_cycle: Dict[str, Any]):
        """Render current trading activity."""
        st.header("⚡ Trading Activity")
        
        if latest_cycle:
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("Latest Cycle")
                st.json({
                    'Cycle Number': latest_cycle.get('cycle_number', 0),
                    'Status': latest_cycle.get('status', 'Unknown'),
                    'Timestamp': latest_cycle.get('timestamp', ''),
                    'Trades Executed': len(latest_cycle.get('trades_executed', []))
                })
            
            with col2:
                st.subheader("System Health")
                
                # System health indicators
                status = latest_cycle.get('status', 'unknown')
                if status == 'completed':
                    st.success("✅ System Operating Normally")
                elif status == 'skipped':
                    st.warning("⚠️ Cycle Skipped")
                else:
                    st.error("❌ System Error")
                
                # Recent trades
                trades = latest_cycle.get('trades_executed', [])
                if trades:
                    st.write("Recent Trades:")
                    for i, trade in enumerate(trades[-3:], 1):  # Show last 3 trades
                        trade_type = trade.get('type', 'unknown')
                        trade_amount = trade.get('sol_amount', 0.0)
                        st.write(f"{i}. {trade_type.upper()} {trade_amount:.4f} SOL")
        else:
            st.warning("No recent trading activity data available")
    
    def render_risk_monitoring(self, production_metrics: Dict[str, Any]):
        """Render risk monitoring section."""
        st.header("🛡️ Risk Monitoring")
        
        portfolio_status = production_metrics.get('portfolio_status', {})
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            exposure_pct = portfolio_status.get('exposure_pct_of_total', 0.0) * 100
            st.metric("Portfolio Exposure", f"{exposure_pct:.1f}%", delta="Of Total Wallet")
            
            # Exposure gauge
            fig = go.Figure(go.Indicator(
                mode="gauge+number",
                value=exposure_pct,
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "Exposure %"},
                gauge={
                    'axis': {'range': [None, 50]},  # Max 50% exposure
                    'bar': {'color': "darkblue"},
                    'steps': [
                        {'range': [0, 25], 'color': "lightgray"},
                        {'range': [25, 40], 'color': "yellow"},
                        {'range': [40, 50], 'color': "red"}
                    ],
                    'threshold': {
                        'line': {'color': "red", 'width': 4},
                        'thickness': 0.75,
                        'value': 50
                    }
                }
            ))
            fig.update_layout(height=200)
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            available_capital = portfolio_status.get('available_capital_usd', 0.0)
            st.metric("Available Capital", f"${available_capital:.2f}")
        
        with col3:
            max_position_size = portfolio_status.get('max_position_size_usd', 0.0)
            st.metric("Max Position Size", f"${max_position_size:.2f}")
    
    def render_alerts_and_notifications(self):
        """Render alerts and notifications section."""
        st.header("🔔 Alerts & Notifications")
        
        # Sample alerts (would be real alerts in production)
        alerts = [
            {"time": "10:30:15", "type": "info", "message": "New trade executed: BUY 0.1234 SOL"},
            {"time": "10:25:42", "type": "success", "message": "Daily profit target reached: +1.2%"},
            {"time": "10:20:18", "type": "warning", "message": "Exposure approaching 40% limit"}
        ]
        
        for alert in alerts:
            if alert["type"] == "success":
                st.success(f"[{alert['time']}] {alert['message']}")
            elif alert["type"] == "warning":
                st.warning(f"[{alert['time']}] {alert['message']}")
            elif alert["type"] == "error":
                st.error(f"[{alert['time']}] {alert['message']}")
            else:
                st.info(f"[{alert['time']}] {alert['message']}")
    
    def run_dashboard(self):
        """Run the production dashboard."""
        # Load data
        production_metrics = self.load_production_metrics()
        wallet_data = self.load_wallet_data()
        latest_cycle = self.load_latest_cycle()
        
        # Render dashboard sections
        self.render_header()
        
        # Main content
        self.render_wallet_overview(wallet_data, production_metrics)
        
        col1, col2 = st.columns(2)
        with col1:
            self.render_performance_metrics(production_metrics)
        with col2:
            self.render_trading_activity(latest_cycle)
        
        self.render_risk_monitoring(production_metrics)
        self.render_alerts_and_notifications()
        
        # Auto-refresh
        st.sidebar.markdown("### Dashboard Controls")
        auto_refresh = st.sidebar.checkbox("Auto Refresh (10s)", value=True)
        
        if auto_refresh:
            import time
            time.sleep(10)
            st.experimental_rerun()


def main():
    """Main function to run the production dashboard."""
    dashboard = ProductionDashboard()
    dashboard.run_dashboard()


if __name__ == "__main__":
    main()
