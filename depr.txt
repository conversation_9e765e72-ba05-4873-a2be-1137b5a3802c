# Deprecated Files List
# This file tracks deprecated files that have been replaced by new implementations

# ===== DEPRECATED DIRECTORIES =====

# Old phase directories (replaced by phase_4_deployment)
phase_0_env_setup/
phase_1_strategy_runner/
phase_2_strategy/
phase_3_rl_agent_training/

# Redundant backtest directories (replaced by phase_4_deployment/backtest)
phase_2_backtest_engine/data/
phase_2_backtest_engine/output/
phase_2_backtest_engine/outputs/

# Backup directories
backup/
backups/
phase_4_deployment/backup_dashboards/

# Redundant output directories
output/
output/dashboard/
output/dashboard_tests/
output/live_trade_test/
output/live_trading_logs/
output/logs/
output/mean_reversion/
output/momentum/
output/paper_trading/
output/production_tests/
output/strategy_comparison/
output/validation/
output/visualizations/

# Redundant data directories
data/
data/prepared/
data/raw/
core/data/

# ===== DEPRECATED FILES =====

# Old data files
phase_0_env_setup/data/historical/sol_usdc.csv
phase_0_env_setup/data/historical/jup_usdc.csv
phase_0_env_setup/data/historical/bonk_usdc.csv

# Old strategy implementations
phase_1_strategy_runner/strategies/momentum_strategy.py
phase_1_strategy_runner/strategies/mean_reversion_strategy.py
phase_1_strategy_runner/strategies/breakout_strategy.py
phase_1_strategy_runner/strategies/basic_ma_strategy.py
phase_1_strategy_runner/strategies/meme_alpha_strategy.py
phase_2_strategy/momentum_strategy.py
phase_2_strategy/run_strategy_finder.py
phase_2_strategy/strategy_finder.py
phase_2_strategy/visualize_strategy.py
core/engine/strategy_runner.py

# Old signal generators
phase_1_strategy_runner/signal_generator.py
phase_1_strategy_runner/signal_processor.py
phase_1_strategy_runner/runners/strategy_runner.py

# Old backtest files
phase_2_backtest_engine/backtest_runner.py
phase_2_backtest_engine/backtest_visualizer.py
phase_2_backtest_engine/backtest_metrics.py
phase_2_backtest_engine/utils/strategy_loader.py

# Old simulation files
phase_4_deployment/simulation/simple_simulator.py
phase_4_deployment/simulation/market_simulator.py

# Redundant dashboard files
phase_4_deployment/backup_dashboards/dashboard_simulator.py
phase_4_deployment/backup_dashboards/run_streamlit_dashboard.py
phase_4_deployment/backup_dashboards/streamlit_dashboard.py
phase_4_deployment/backup_dashboards/test_streamlit_dashboard.py
phase_4_deployment/fix_dashboard.py
phase_4_deployment/remove_old_dashboards.py
phase_4_deployment/verify_metrics_dashboard.py
import_dashboard.py
run_dashboard.py

# Redundant test files
phase_4_deployment/test_birdeye.py
phase_4_deployment/test_helius.py
phase_4_deployment/test_monitoring_components.py
phase_4_deployment/test_monitoring.py
phase_4_deployment/test_paper_trading.py
phase_4_deployment/test_python_comm_layer.py
phase_4_deployment/test_stream_data_ingestor.py
phase_4_deployment/test_system.py
phase_4_deployment/test_transaction.py
test_birdeye_api.py
test_carbon_core.py
test_helius.py
test_solana_tx_utils.py
test_transaction.py

# Redundant configuration files
config_example.yaml
configs/config_example.yaml

# ===== ADDITIONAL DEPRECATED FILES (Integration Plan) =====

# Old market regime detection (replaced by enhanced version)
phase_2_strategy/market_regime.py

# Redundant strategy implementations (replaced by unified core strategies)
phase_2_strategy/momentum_strategy.py
phase_2_strategy/mean_reversion.py
phase_2_strategy/risk_management.py
backup/mean_reversion/

# Old whale watching (replaced by enhanced signal generator)
phase_4_deployment/data_router/whale_watcher.py
phase_4_deployment/data_router/birdeye_scanner.py

# Redundant risk management (replaced by enhanced core risk)
phase_2_strategy/risk_management.py

# Old strategy runners (replaced by unified runner)
phase_1_strategy_runner/runners/strategy_runner.py
core/engine/strategy_runner.py

# Redundant configuration files
live_trade_test_config.yaml
paper_trade_config.yaml
carbon_core_config.yaml
carbon_core_fallback_config.yaml

# Redundant test files
test_telegram.py
test_trade_notification.py
test_trading_alerts.py
direct_telegram_test.py

# Redundant runner files
run_synergy7.py
run_q5_system.py
start_live_trading_local.py
unified_runner.py (root level)
paper_trading_simulator.py

# ===== REPLACEMENT INFORMATION =====

# These deprecated files and directories have been replaced by the new implementations in:
# - core/strategies/ - Enhanced strategy implementations with regime detection
# - core/data/ - Unified data ingestion and whale signal generation
# - core/risk/ - Advanced risk management with VaR/CVaR
# - core/analytics/ - Strategy performance attribution and analysis
# - core/signals/ - Unified signal processing and enrichment
# - phase_4_deployment/unified_runner.py - Single entry point for all modes
# - config.yaml - Centralized configuration with all parameters

# The new integrated system provides:
# 1. Enhanced market regime detection with probabilistic models
# 2. Whale watching integration as complementary alpha source
# 3. Advanced risk management with VaR/CVaR and correlation analysis
# 4. Strategy performance attribution and adaptive weighting
# 5. Configuration-driven architecture avoiding hard-coded values
# 6. Unified entry point with proper mode switching
# 7. Comprehensive monitoring and alerting system

# ===== ENHANCED INTEGRATION FILES (DO NOT DELETE) =====

# Phase 1: Enhanced Market Regime Detection & Whale Watching
# core/strategies/market_regime_detector.py - Enhanced regime detection with dynamic thresholds
# core/strategies/probabilistic_regime.py - ML-based regime detection with HMM
# core/data/whale_signal_generator.py - Whale transaction monitoring and signal generation
# core/signals/whale_signal_processor.py - Whale signal processing and validation

# Phase 2: Advanced Risk Management
# core/risk/var_calculator.py - VaR/CVaR calculation with multiple methodologies
# core/risk/portfolio_risk_manager.py - Portfolio-level risk monitoring and correlation analysis
# core/risk/position_sizer.py (enhanced) - VaR-based and regime-aware position sizing

# Phase 3: Strategy Performance Attribution
# core/analytics/strategy_attribution.py - Individual strategy performance tracking
# core/analytics/performance_analyzer.py - Portfolio analysis and optimization recommendations

# Phase 4: Adaptive Strategy Weighting
# core/strategies/adaptive_weight_manager.py - Dynamic weight adjustment based on performance
# core/strategies/strategy_selector.py - Intelligent strategy selection and management

# Integration Test Files
# test_phase1_integration.py - Phase 1 validation tests
# test_phase2_simple.py - Phase 2 validation tests
# test_phase3_attribution.py - Phase 3 validation tests
# test_phase4_adaptive_weighting.py - Phase 4 validation tests
# test_complete_integration.py - End-to-end integration validation

# Documentation Files
# PHASE1_IMPLEMENTATION_SUMMARY.md - Phase 1 detailed documentation
# PHASE2_IMPLEMENTATION_SUMMARY.md - Phase 2 detailed documentation
# PHASE4_IMPLEMENTATION_SUMMARY.md - Phase 4 detailed documentation
# COMPLETE_INTEGRATION_SUMMARY.md - Comprehensive integration summary
# DEPLOYMENT_CHECKLIST.md - Production deployment checklist

# Enhanced Configuration
# config.yaml (enhanced) - Comprehensive configuration with all new parameters

# ===== REDUNDANT SCRIPTS (SUPERSEDED BY ENHANCED TRADING SYSTEM) =====

# Old paper trading implementations (replaced by simple_paper_trading_monitor.py)
start_paper_trading.py
run_devnet_paper_trade.py
paper_trading_simulator.py

# Old dashboard implementations (replaced by enhanced_trading_dashboard.py and simple_monitoring_dashboard.py)
import_dashboard.py
run_dashboard.py
phase_4_deployment/run_monitoring.py
phase_4_deployment/unified_dashboard/run_dashboard.py
phase_4_deployment/monitoring/streamlit_dashboard.py
phase_4_deployment/fix_dashboard.py
phase_4_deployment/verify_metrics_dashboard.py
phase_4_deployment/remove_old_dashboards.py

# Old monitoring implementations (replaced by enhanced monitoring suite)
core/monitoring/performance_monitor.py
utils/monitoring/monitoring_service.py
shared/utils/monitoring.py
scripts/system_health_monitor.py
scripts/test_monitoring_setup.py
phase_4_deployment/monitoring/monitoring_service.py
phase_4_deployment/monitoring/setup_monitoring.py
phase_4_deployment/monitoring/mock_monitoring_service.py
phase_4_deployment/monitoring/pyo3_extension_monitor.py
phase_4_deployment/utils/monitoring.py
phase_4_deployment/core/jito_monitor.py

# Old test files (replaced by enhanced validation system)
test_telegram.py
test_transaction.py
test_helius.py
direct_telegram_test.py
test_phase2_risk_management.py
test_phase4_adaptive_weighting.py
phase_4_deployment/test_monitoring_components.py
phase_4_deployment/test_monitoring.py
phase_4_deployment/test_paper_trading.py
phase_4_deployment/test_birdeye.py
phase_4_deployment/test_helius.py
phase_4_deployment/test_system.py
phase_4_deployment/test_transaction.py
phase_4_deployment/tests/test_unified_dashboard.py
phase_4_deployment/tests/test_streamlit_dashboard.py
phase_4_deployment/scripts/test_dashboard.py

# Old runner implementations (replaced by enhanced paper trading monitor)
run_synergy7.py
run_q5_system.py
start_live_trading_local.py
unified_runner.py (root level)

# Old setup and utility scripts (replaced by enhanced system)
setup_production_env.py
validate_production_setup.py
generate_test_metrics.py
identify_duplicates.py
update_imports.py
run_tests.py

# Failed enhanced implementation (replaced by simple_paper_trading_monitor.py)
enhanced_paper_trading_monitor.py

# Backup dashboard files (all superseded)
phase_4_deployment/backup_dashboards/dashboard_simulator.py
phase_4_deployment/backup_dashboards/run_streamlit_dashboard.py
phase_4_deployment/backup_dashboards/streamlit_dashboard.py
phase_4_deployment/backup_dashboards/test_streamlit_dashboard.py
phase_4_deployment/backup_dashboards/test_monitoring_components.py

# ===== REPLACEMENT SUMMARY FOR ENHANCED TRADING SYSTEM =====

# The following files have been REPLACED by the Enhanced Trading System:

# OLD PAPER TRADING → NEW ENHANCED PAPER TRADING
# start_paper_trading.py → simple_paper_trading_monitor.py
# run_devnet_paper_trade.py → simple_paper_trading_monitor.py
# paper_trading_simulator.py → simple_paper_trading_monitor.py

# OLD DASHBOARDS → NEW DASHBOARD SUITE
# import_dashboard.py → enhanced_trading_dashboard.py
# run_dashboard.py → enhanced_trading_dashboard.py
# phase_4_deployment/unified_dashboard/run_dashboard.py → enhanced_trading_dashboard.py
# phase_4_deployment/monitoring/streamlit_dashboard.py → simple_monitoring_dashboard.py

# OLD MONITORING → NEW MONITORING SUITE
# core/monitoring/performance_monitor.py → core/monitoring/system_metrics.py
# scripts/system_health_monitor.py → simple_monitoring_dashboard.py
# phase_4_deployment/monitoring/monitoring_service.py → simple_monitoring_dashboard.py

# OLD VALIDATION → NEW ENHANCED VALIDATION
# test_phase2_risk_management.py → Integrated in simple_paper_trading_monitor.py
# test_phase4_adaptive_weighting.py → Integrated in simple_paper_trading_monitor.py
# validate_production_setup.py → Integrated validation in enhanced system

# The Enhanced Trading System provides:
# 1. ✅ Real-time 4-phase trading system simulation
# 2. ✅ Comprehensive dashboard suite with live monitoring
# 3. ✅ Integrated validation through paper trading cycles
# 4. ✅ System health monitoring with API status tracking
# 5. ✅ Performance attribution and adaptive strategy weighting
# 6. ✅ VaR/CVaR risk management with real-time calculations
# 7. ✅ Market regime detection with confidence levels
# 8. ✅ Whale signal monitoring and processing
# 9. ✅ Auto-refresh dashboards with configurable intervals
# 10. ✅ Comprehensive data generation for analysis

# ===== TRANSACTION FIXES - DEPRECATED FILES (2025-05-24) =====

# Old transaction implementations (replaced by enhanced versions)
phase_4_deployment/rpc_execution/transaction_executor.py → core/transaction/enhanced_tx_executor.py
phase_4_deployment/rpc_execution/tx_builder.py → core/transaction/enhanced_tx_builder.py
phase_4_deployment/start_live_trading.py → scripts/enhanced_live_trading.py
wallet/trading_wallet_keypair.json (old invalid keypair) → wallet/trading_wallet_keypair.json (new valid)

# Redundant environment files (can be archived)
.env.active_simulation.bak
.env.bak.bak
.env.backup.bak
.env.simulation.bak
phase_4_deployment/sample.env

# ===== ACTIVE ENHANCED SYSTEM FILES (DO NOT DEPRECATE) =====

# Enhanced Transaction System (NEW - 2025-05-24):
# core/transaction/enhanced_tx_builder.py - Complete transaction building with Jupiter integration
# core/transaction/enhanced_tx_executor.py - Multi-RPC execution with retry logic
# core/wallet/secure_wallet_manager.py - Secure keypair management and validation
# scripts/enhanced_live_trading.py - Main enhanced live trading system
# scripts/test_transaction_fixes.py - Comprehensive transaction validation
# scripts/generate_test_keypair.py - Secure keypair generation

# Enhanced Trading System Core Files:
# simple_paper_trading_monitor.py - Main enhanced paper trading monitor
# enhanced_trading_dashboard.py - Real-time trading strategy dashboard
# simple_monitoring_dashboard.py - System health and API monitoring dashboard

# Enhanced Core Components:
# core/strategies/market_regime_detector.py - Enhanced market regime detection
# core/strategies/probabilistic_regime.py - Probabilistic regime detection
# core/strategies/adaptive_weight_manager.py - Adaptive strategy weighting
# core/strategies/strategy_selector.py - Intelligent strategy selection
# core/risk/var_calculator.py - VaR/CVaR risk calculations
# core/risk/portfolio_risk_manager.py - Portfolio risk management
# core/data/whale_signal_generator.py - Whale transaction monitoring
# core/signals/whale_signal_processor.py - Whale signal processing
# core/analytics/strategy_attribution.py - Strategy performance attribution
# core/analytics/performance_analyzer.py - Performance analysis
# core/monitoring/system_metrics.py - System health monitoring

# Enhanced Documentation:
# ENHANCED_TRADING_SYSTEM.md - Comprehensive enhanced system documentation
# ENHANCED_SYSTEM_SUMMARY.md - Implementation summary and validation results
# README.md (updated) - Main system overview with enhanced features
# integration_plan.md (updated) - Phase 3 completion status
